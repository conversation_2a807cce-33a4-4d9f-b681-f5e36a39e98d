# 📝 Guía de Desarrollo de Prompts - AgentQA

Esta guía explica cómo crear nuevos prompts y usarlos en el código de AgentQA.

## 📋 Tabla de Contenidos

1. [Estructura del Sistema](#estructura-del-sistema)
2. [Crear un Nuevo Prompt](#crear-un-nuevo-prompt)
3. [Usar Prompts en Código](#usar-prompts-en-código)
4. [Ejemplos Prácticos](#ejemplos-prácticos)
5. [Validación y Testing](#validación-y-testing)
6. [Mejores Prácticas](#mejores-prácticas)

## 🏗️ Estructura del Sistema

### Directorios de Prompts

```
prompts/
├── browser-automation/     # Automatización de navegador
├── code-generation/        # Generación de código
├── test-analysis/          # Análisis de resultados
├── test-cases/            # Casos de prueba
├── user-story/            # Historias de usuario
└── [nueva-categoria]/     # Tu nueva categoría
```

### Archivos por Categoría

Cada categoría contiene:
- `metadata.json` - Metadatos de la categoría
- `[prompt-id].md` - Archivos de prompts individuales

## 🆕 Crear un Nuevo Prompt

### Paso 1: Crear la Estructura de Archivos

#### 1.1 Crear Directorio (si es nueva categoría)

```bash
mkdir prompts/mi-nueva-categoria
```

#### 1.2 Crear metadata.json

```json
{
  "category": "mi-nueva-categoria",
  "version": "1.0.0",
  "description": "Descripción de la categoría",
  "prompts": [
    {
      "id": "mi-prompt",
      "name": "Mi Nuevo Prompt",
      "description": "Descripción del prompt",
      "file": "mi-prompt.md",
      "languages": ["en", "es"],
      "variables": ["variable1", "variable2"],
      "tags": ["tag1", "tag2"],
      "lastModified": "2024-12-19T12:00:00Z",
      "author": "Tu Nombre",
      "dependencies": [],
      "complexity": "medium",
      "estimatedTokens": 800
    }
  ],
  "shared_variables": {
    "variable1": {
      "type": "string",
      "description": "Descripción de la variable",
      "required": true,
      "examples": ["ejemplo1", "ejemplo2"]
    }
  }
}
```

### Paso 2: Crear el Archivo Markdown

#### 2.1 Estructura Requerida

```markdown
# Mi Nuevo Prompt

## Purpose
Descripción clara del propósito del prompt.

## Input Format
- Formato de entrada esperado
- Tipos de datos requeridos

## Output Format
- Formato de salida esperado
- Estructura de la respuesta

## English Prompt

Tu prompt en inglés aquí.

Variables disponibles:
- {variable1}: Descripción
- {variable2}: Descripción

Instrucciones específicas...

## Spanish Prompt

Tu prompt en español aquí.

Variables disponibles:
- {variable1}: Descripción
- {variable2}: Descripción

Instrucciones específicas...

## Variables
- `variable1`: Descripción de la variable 1
- `variable2`: Descripción de la variable 2

## Examples

### Input
```
Ejemplo de entrada
```

### Output
```
Ejemplo de salida esperada
```
```

#### 2.2 Ejemplo Completo

Crear `prompts/mi-nueva-categoria/mi-prompt.md`:

```markdown
# Generador de Documentación

## Purpose
Genera documentación técnica a partir de código fuente.

## Input Format
- Código fuente en cualquier lenguaje
- Contexto adicional opcional

## Output Format
- Documentación en formato Markdown
- Incluye descripción, parámetros y ejemplos

## English Prompt

Generate comprehensive technical documentation for the following code:

```
{source_code}
```

Context: {context}

Please provide:
1. Clear description of functionality
2. Parameter documentation
3. Return value description
4. Usage examples
5. Any important notes or warnings

Format the output as clean Markdown.

## Spanish Prompt

Genera documentación técnica completa para el siguiente código:

```
{source_code}
```

Contexto: {context}

Por favor proporciona:
1. Descripción clara de la funcionalidad
2. Documentación de parámetros
3. Descripción del valor de retorno
4. Ejemplos de uso
5. Notas importantes o advertencias

Formatea la salida como Markdown limpio.

## Variables
- `source_code`: El código fuente a documentar
- `context`: Contexto adicional sobre el código

## Examples

### Input
```python
def calculate_total(items, tax_rate=0.1):
    subtotal = sum(item.price for item in items)
    return subtotal * (1 + tax_rate)
```

### Output
```markdown
# calculate_total

Calcula el total de una lista de items incluyendo impuestos.

## Parámetros
- `items`: Lista de objetos con atributo `price`
- `tax_rate`: Tasa de impuesto (default: 0.1 = 10%)

## Retorna
- `float`: Total calculado con impuestos incluidos
```
```

## 💻 Usar Prompts en Código

### Método 1: Ejecución con LLM (execute_prompt)

Para prompts que necesitan procesamiento de IA:

```python
from src.Core.prompt_service import PromptService

# Inicializar el servicio
prompt_service = PromptService()

# Ejecutar prompt con LLM
result = prompt_service.execute_prompt(
    category="mi-nueva-categoria",
    prompt_id="mi-prompt", 
    language="es",
    variable1="valor1",
    variable2="valor2"
)

print(result)  # Respuesta del LLM
```

### Método 2: Sustitución de Variables (substitute_template)

Para prompts que solo necesitan sustitución de variables:

```python
from src.Core.prompt_service import PromptService

# Inicializar el servicio
prompt_service = PromptService()

# Solo sustituir variables (sin LLM)
template = prompt_service.substitute_template(
    category="browser-automation",
    prompt_id="task-generation",
    language="en",
    scenario="Login to the application",
    base_url="https://example.com"
)

print(template)  # Template con variables sustituidas
```

### Método 3: Crear Método de Conveniencia

Agregar método específico en `PromptService`:

```python
# En src/Core/prompt_service.py

def generate_documentation(self, source_code: str, context: str = "", language: str = "en") -> str:
    """Generate technical documentation from source code.
    
    Args:
        source_code: Source code to document
        context: Additional context about the code
        language: Language for the prompt ('en' or 'es')
        
    Returns:
        Generated documentation
    """
    return self.execute_prompt(
        "mi-nueva-categoria", 
        "mi-prompt", 
        language, 
        source_code=source_code,
        context=context
    )
```

Uso:

```python
# Usar el método de conveniencia
documentation = prompt_service.generate_documentation(
    source_code="def hello(): return 'world'",
    context="Simple greeting function",
    language="es"
)
```

## 🔧 Ejemplos Prácticos

### Ejemplo 1: Prompt de Análisis de Código

```python
# Crear prompt para análisis de código
from src.Core.prompt_service import PromptService

prompt_service = PromptService()

# Ejecutar análisis
analysis = prompt_service.execute_prompt(
    category="code-analysis",
    prompt_id="security-review",
    language="en",
    code_snippet="""
    def login(username, password):
        query = f"SELECT * FROM users WHERE username='{username}' AND password='{password}'"
        return execute_query(query)
    """,
    security_level="high"
)

print("Análisis de seguridad:", analysis)
```

### Ejemplo 2: Prompt de Generación de Tests

```python
# Generar tests automáticamente
test_code = prompt_service.execute_prompt(
    category="test-generation",
    prompt_id="unit-tests",
    language="es",
    function_code="def add(a, b): return a + b",
    test_framework="pytest"
)

print("Tests generados:", test_code)
```

### Ejemplo 3: Template para Configuración

```python
# Solo sustitución de variables para configuración
config_template = prompt_service.substitute_template(
    category="configuration",
    prompt_id="docker-setup",
    language="en",
    app_name="my-app",
    port="3000",
    environment="production"
)

# Guardar configuración
with open("Dockerfile", "w") as f:
    f.write(config_template)
```

## ✅ Validación y Testing

### Validar Prompt Individual

```python
from src.Core.prompt_validator import PromptValidator

validator = PromptValidator("prompts")

# Validar un prompt específico
is_valid = validator.validate_prompt_file("prompts/mi-categoria/mi-prompt.md")
print(f"Prompt válido: {is_valid}")
```

### Validar Todos los Prompts

```python
# Validar toda la categoría
results = validator.validate_category("mi-categoria")
print("Resultados de validación:", results)

# Validar todo el sistema
all_results = validator.validate_all_prompts()
for category, result in all_results.items():
    print(f"{category}: {result}")
```

### Testing Manual

```python
# Test rápido del prompt
try:
    result = prompt_service.execute_prompt(
        "mi-categoria",
        "mi-prompt",
        "en",
        variable1="test_value",
        variable2="another_test"
    )
    print("✅ Prompt funciona correctamente")
    print("Resultado:", result[:100] + "...")
except Exception as e:
    print("❌ Error:", e)
```

## 🎯 Mejores Prácticas

### 1. Nomenclatura

- **Categorías**: kebab-case (`user-story`, `code-generation`)
- **Prompt IDs**: kebab-case (`enhance`, `security-review`)
- **Variables**: snake_case (`user_story`, `source_code`)

### 2. Estructura de Prompts

- **Sé específico**: Define claramente qué esperas como entrada y salida
- **Usa ejemplos**: Incluye ejemplos concretos en la sección Examples
- **Soporte multiidioma**: Siempre incluye versiones en inglés y español
- **Variables claras**: Documenta todas las variables en la sección Variables

### 3. Gestión de Variables

```python
# ✅ Bueno: Variables claras y descriptivas
prompt_service.execute_prompt(
    "code-generation",
    "selenium-pytest",
    "en",
    gherkin_steps=scenario,
    base_url=url,
    selectors=json.dumps(selectors),
    actions=json.dumps(actions)
)

# ❌ Malo: Variables ambiguas
prompt_service.execute_prompt(
    "code-generation", 
    "selenium-pytest",
    "en",
    data=some_data,
    info=other_info
)
```

### 4. Manejo de Errores

```python
try:
    result = prompt_service.execute_prompt(
        category="mi-categoria",
        prompt_id="mi-prompt",
        language="es",
        **variables
    )
    return result
except FileNotFoundError:
    logger.error(f"Prompt no encontrado: {category}:{prompt_id}")
    return "Error: Prompt no disponible"
except Exception as e:
    logger.error(f"Error ejecutando prompt: {e}")
    return f"Error: {str(e)}"
```

### 5. Performance

```python
# Reutilizar instancia del servicio
class MyService:
    def __init__(self):
        self.prompt_service = PromptService()  # Una sola instancia
    
    def process_data(self, data):
        # Reutilizar la misma instancia
        return self.prompt_service.execute_prompt(...)
```

## 🚀 Casos de Uso Avanzados

### 1. Prompts con Contexto Dinámico

```python
# Prompt que adapta su comportamiento según el contexto
def generate_adaptive_prompt(self, task_type: str, complexity: str, **data):
    """Genera prompts adaptativos según el contexto."""

    # Seleccionar prompt según el tipo de tarea
    prompt_mapping = {
        "simple": "basic-generation",
        "complex": "advanced-generation",
        "expert": "expert-generation"
    }

    prompt_id = prompt_mapping.get(complexity, "basic-generation")

    return self.prompt_service.execute_prompt(
        category="adaptive-prompts",
        prompt_id=prompt_id,
        language="es",
        task_type=task_type,
        **data
    )
```

### 2. Prompts en Cadena (Chaining)

```python
# Ejecutar múltiples prompts en secuencia
def process_user_story_pipeline(self, raw_story: str):
    """Pipeline completo de procesamiento de historia de usuario."""

    # Paso 1: Mejorar historia
    enhanced_story = self.prompt_service.enhance_user_story(raw_story, "es")

    # Paso 2: Generar casos de prueba
    test_cases = self.prompt_service.generate_test_cases(enhanced_story, "es")

    # Paso 3: Convertir a Gherkin
    gherkin = self.prompt_service.generate_gherkin(test_cases, "es")

    return {
        "enhanced_story": enhanced_story,
        "test_cases": test_cases,
        "gherkin": gherkin
    }
```

### 3. Prompts con Validación de Salida

```python
def generate_with_validation(self, category: str, prompt_id: str, **variables):
    """Ejecuta prompt con validación de salida."""

    max_retries = 3
    for attempt in range(max_retries):
        try:
            result = self.prompt_service.execute_prompt(
                category, prompt_id, "en", **variables
            )

            # Validar resultado
            if self.validate_output(result, expected_format="json"):
                return result
            else:
                logger.warning(f"Intento {attempt + 1}: Salida inválida")

        except Exception as e:
            logger.error(f"Intento {attempt + 1} falló: {e}")

    raise Exception("No se pudo generar salida válida después de 3 intentos")
```

## 🔧 Troubleshooting

### Problemas Comunes

#### 1. Error: "Prompt not found"

```python
# ❌ Error común
result = prompt_service.execute_prompt("wrong-category", "wrong-id", "en")

# ✅ Solución: Verificar prompts disponibles
available = prompt_service.get_available_prompts()
print("Categorías disponibles:", list(available.keys()))
print("Prompts en categoría:", available.get("user-story", []))
```

#### 2. Error: "Missing required variable"

```python
# ❌ Error: Variable faltante
result = prompt_service.execute_prompt("user-story", "enhance", "en")
# Error: Missing required variable 'user_story'

# ✅ Solución: Proporcionar todas las variables
result = prompt_service.execute_prompt(
    "user-story", "enhance", "en",
    user_story="Como usuario quiero..."
)
```

#### 3. Error: "Language not supported"

```python
# ❌ Error: Idioma no soportado
result = prompt_service.execute_prompt("user-story", "enhance", "fr")

# ✅ Solución: Usar idiomas soportados
result = prompt_service.execute_prompt("user-story", "enhance", "es")  # o "en"
```

### Debugging

#### 1. Verificar Variables del Prompt

```python
# Obtener información del prompt
info = prompt_service.get_prompt_info("user-story", "enhance")
print("Variables requeridas:", info["variables"])
print("Idiomas soportados:", info["languages"])
```

#### 2. Validar Template sin Ejecutar

```python
# Solo sustituir variables para debug
template = prompt_service.substitute_template(
    "user-story", "enhance", "es",
    user_story="Historia de prueba"
)
print("Template generado:", template)
```

#### 3. Limpiar Cache

```python
# Si hay problemas de cache
prompt_service.clear_cache()
```

## 📊 Monitoreo y Métricas

### 1. Logging de Prompts

```python
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def execute_with_logging(self, category: str, prompt_id: str, **kwargs):
    """Ejecuta prompt con logging detallado."""

    start_time = time.time()
    logger.info(f"Ejecutando prompt: {category}:{prompt_id}")

    try:
        result = self.prompt_service.execute_prompt(category, prompt_id, **kwargs)
        execution_time = time.time() - start_time

        logger.info(f"Prompt ejecutado exitosamente en {execution_time:.2f}s")
        logger.info(f"Longitud de respuesta: {len(result)} caracteres")

        return result

    except Exception as e:
        logger.error(f"Error ejecutando prompt: {e}")
        raise
```

### 2. Métricas de Performance

```python
class PromptMetrics:
    def __init__(self):
        self.execution_times = {}
        self.success_rates = {}

    def track_execution(self, category: str, prompt_id: str, execution_time: float, success: bool):
        key = f"{category}:{prompt_id}"

        if key not in self.execution_times:
            self.execution_times[key] = []
            self.success_rates[key] = {"success": 0, "total": 0}

        self.execution_times[key].append(execution_time)
        self.success_rates[key]["total"] += 1
        if success:
            self.success_rates[key]["success"] += 1

    def get_stats(self, category: str, prompt_id: str):
        key = f"{category}:{prompt_id}"
        times = self.execution_times.get(key, [])
        rates = self.success_rates.get(key, {"success": 0, "total": 0})

        return {
            "avg_time": sum(times) / len(times) if times else 0,
            "success_rate": rates["success"] / rates["total"] if rates["total"] > 0 else 0,
            "total_executions": rates["total"]
        }
```

---

## 📚 Referencias

- [Documentación de PromptService](../src/Core/prompt_service.py)
- [Validador de Prompts](../src/Core/prompt_validator.py)
- [Ejemplos de Prompts](../prompts/)
- [API de Prompts](../src/API/prompt_routes.py)

## 🆘 Soporte

### Comandos Útiles

```bash
# Validar todos los prompts
python -c "from src.Core.prompt_validator import PromptValidator; print(PromptValidator('prompts').validate_all_prompts())"

# Listar prompts disponibles
python -c "from src.Core.prompt_service import PromptService; print(PromptService().get_available_prompts())"

# Test rápido de un prompt
python -c "from src.Core.prompt_service import PromptService; print(PromptService().execute_prompt('user-story', 'enhance', 'en', user_story='test'))"
```

### Checklist de Desarrollo

- [ ] ✅ Crear metadata.json con información completa
- [ ] ✅ Crear archivo .md con estructura estándar
- [ ] ✅ Incluir versiones en inglés y español
- [ ] ✅ Documentar todas las variables
- [ ] ✅ Agregar ejemplos de entrada y salida
- [ ] ✅ Validar prompt con PromptValidator
- [ ] ✅ Crear tests unitarios
- [ ] ✅ Agregar método de conveniencia en PromptService (opcional)
- [ ] ✅ Documentar el nuevo prompt

---

**¿Necesitas ayuda?** Revisa los prompts existentes en `/prompts/` como ejemplos o consulta la documentación del código.
