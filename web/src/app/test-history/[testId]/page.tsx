'use client';

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertCircle, ArrowLeft, Calendar, ExternalLink, Image as ImageIcon, Code, Download } from 'lucide-react';
import { getTestHistoryDetails, getTestScreenshot } from '@/lib/api';
import Link from 'next/link';

export default function TestHistoryDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const testId = params.testId as string;

  const { data: testDetails, isLoading, error } = useQuery({
    queryKey: ['testHistoryDetails', testId],
    queryFn: () => getTestHistoryDetails(testId),
    enabled: !!testId,
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('es-ES', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  const getResultBadgeVariant = (result: string) => {
    if (result?.toLowerCase().includes('success') || result?.toLowerCase().includes('exitoso')) {
      return 'default';
    }
    if (result?.toLowerCase().includes('error') || result?.toLowerCase().includes('fail')) {
      return 'destructive';
    }
    return 'secondary';
  };

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="flex items-center justify-center p-6">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Error al cargar los detalles</h3>
              <p className="text-muted-foreground">
                No se pudieron cargar los detalles del test. Por favor, intenta de nuevo.
              </p>
              <Button onClick={() => router.back()} className="mt-4">
                Volver
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center gap-4">
          <Skeleton className="h-10 w-10" />
          <div className="space-y-2">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-48" />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Skeleton className="h-32" />
          <Skeleton className="h-32" />
          <Skeleton className="h-32" />
        </div>
        <Skeleton className="h-96" />
      </div>
    );
  }

  const history = testDetails?.history;
  const screenshots = testDetails?.screenshots || [];

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Volver
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Detalles del Test</h1>
          <p className="text-muted-foreground">ID: {testId}</p>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Resultado</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              <Badge variant={getResultBadgeVariant(history?.final_result || 'Completado')}>
                {history?.final_result || 'Completado'}
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Acciones Ejecutadas</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{history?.action_names?.length || 0}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Capturas de Pantalla</CardTitle>
            <ImageIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{screenshots.length}</div>
          </CardContent>
        </Card>
      </div>

      {/* Main URL */}
      {history?.urls && history.urls.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>URL Principal</CardTitle>
          </CardHeader>
          <CardContent>
            <a 
              href={history.urls[0]} 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-blue-600 hover:text-blue-800 flex items-center gap-2"
            >
              {history.urls[0]}
              <ExternalLink className="h-4 w-4" />
            </a>
          </CardContent>
        </Card>
      )}

      {/* Detailed Information */}
      <Tabs defaultValue="actions" className="space-y-4">
        <TabsList>
          <TabsTrigger value="actions">Acciones</TabsTrigger>
          <TabsTrigger value="screenshots">Capturas</TabsTrigger>
          <TabsTrigger value="errors">Errores</TabsTrigger>
          <TabsTrigger value="metadata">Metadatos</TabsTrigger>
        </TabsList>

        <TabsContent value="actions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Acciones Ejecutadas</CardTitle>
              <CardDescription>
                Secuencia de acciones realizadas durante la ejecución del test
              </CardDescription>
            </CardHeader>
            <CardContent>
              {history?.action_names && history.action_names.length > 0 ? (
                <div className="space-y-4">
                  {history.action_names.map((action: string, index: number) => (
                    <div key={index} className="flex items-start gap-4 p-4 border rounded-lg">
                      <div className="flex-shrink-0 w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-medium">
                        {index + 1}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium">{action}</h4>
                        {history.model_actions && history.model_actions[index] && (
                          <div className="mt-2 text-sm text-muted-foreground">
                            <pre className="whitespace-pre-wrap bg-muted p-2 rounded text-xs overflow-x-auto">
                              {JSON.stringify(history.model_actions[index], null, 2)}
                            </pre>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground">No se registraron acciones en este test.</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="screenshots" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Capturas de Pantalla</CardTitle>
              <CardDescription>
                Capturas tomadas durante la ejecución del test
              </CardDescription>
            </CardHeader>
            <CardContent>
              {screenshots.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {screenshots.map((screenshot: string, index: number) => {
                    const filename = screenshot.split('/').pop() || '';
                    const screenshotUrl = getTestScreenshot(testId, filename);
                    
                    return (
                      <div key={index} className="space-y-2">
                        <div className="aspect-video bg-muted rounded-lg overflow-hidden">
                          <img
                            src={screenshotUrl}
                            alt={`Captura ${index + 1}`}
                            className="w-full h-full object-cover cursor-pointer hover:scale-105 transition-transform"
                            onClick={() => window.open(screenshotUrl, '_blank')}
                          />
                        </div>
                        <p className="text-sm text-muted-foreground text-center">
                          Captura {index + 1}
                        </p>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <p className="text-muted-foreground">No se capturaron pantallas durante este test.</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="errors" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Errores</CardTitle>
              <CardDescription>
                Errores encontrados durante la ejecución
              </CardDescription>
            </CardHeader>
            <CardContent>
              {history?.errors && history.errors.length > 0 ? (
                <div className="space-y-4">
                  {history.errors.map((error: string, index: number) => (
                    <div key={index} className="p-4 border border-destructive/20 bg-destructive/5 rounded-lg">
                      <h4 className="font-medium text-destructive mb-2">Error {index + 1}</h4>
                      <pre className="text-sm text-muted-foreground whitespace-pre-wrap">{error}</pre>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground">No se encontraron errores en este test.</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="metadata" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Metadatos del Test</CardTitle>
              <CardDescription>
                Información técnica y metadatos de la ejecución
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium mb-2">URLs Visitadas</h4>
                    {history?.urls && history.urls.length > 0 ? (
                      <ul className="space-y-1">
                        {history.urls.map((url: string, index: number) => (
                          <li key={index} className="text-sm text-muted-foreground">
                            {index + 1}. {url}
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <p className="text-sm text-muted-foreground">No hay URLs registradas</p>
                    )}
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">Contenido Extraído</h4>
                    {history?.extracted_content && history.extracted_content.length > 0 ? (
                      <div className="space-y-2">
                        {history.extracted_content.slice(0, 3).map((content: string, index: number) => (
                          <div key={index} className="text-sm text-muted-foreground p-2 bg-muted rounded">
                            {content.length > 100 ? `${content.substring(0, 100)}...` : content}
                          </div>
                        ))}
                        {history.extracted_content.length > 3 && (
                          <p className="text-xs text-muted-foreground">
                            Y {history.extracted_content.length - 3} elementos más...
                          </p>
                        )}
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground">No hay contenido extraído</p>
                    )}
                  </div>
                </div>

                {/* Raw JSON Data */}
                <div>
                  <h4 className="font-medium mb-2">Datos Completos (JSON)</h4>
                  <div className="bg-muted p-4 rounded-lg overflow-x-auto">
                    <pre className="text-xs">
                      {JSON.stringify(history, null, 2)}
                    </pre>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
