// src/components/AppSidebar.tsx
"use client";

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Sidebar,
  SidebarHeader,
  SidebarContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarFooter,
  SidebarSeparator,
} from '@/components/ui/sidebar';
import { Bot, FolderKanban, Settings, TestTube2, LayoutDashboard, Flame, Wand2, FileText, History } from 'lucide-react';
import type { NavItem } from '@/lib/types';

const mainNavItems: NavItem[] = [
  { title: 'Dashboard', href: '/', icon: <LayoutDashboard /> },
  { title: 'Projects', href: '/projects', icon: <FolderKanban /> },
  { title: 'AI Tools', href: '/ai-tools', icon: <Bot /> },
  { title: 'Smoke Test Playground', href: '/smoke-test-playground', icon: <Flame /> },
  { title: 'Test History', href: '/test-history', icon: <History /> },
  { title: 'Prompt Management', href: '/prompts', icon: <FileText /> },
  { title: 'QA Assistant', href: '/qa-assistant', icon: <Wand2 /> }, // New Item
];

const settingsNavItem: NavItem = {
  title: 'Settings',
  href: '/settings',
  icon: <Settings />,
};

export function AppSidebar() {
  const pathname = usePathname();

  const isActive = (href: string) => {
    if (href === '/') return pathname === href;
    return pathname.startsWith(href);
  };

  return (
    <Sidebar collapsible="icon">
      <SidebarHeader className="p-4">
        <Link href="/" className="flex items-center gap-2 glow-text group-data-[collapsible=icon]:justify-center">
          <TestTube2 className="h-7 w-7 text-primary" />
          <span className="text-xl font-semibold group-data-[collapsible=icon]:hidden">QA Killer</span>
        </Link>
      </SidebarHeader>
      <SidebarContent>
        <SidebarMenu>
          {mainNavItems.map((item) => (
            <SidebarMenuItem key={item.href}>
              <Link href={item.href} legacyBehavior passHref>
                <SidebarMenuButton
                  asChild
                  isActive={isActive(item.href)}
                  tooltip={{ children: item.title, className: "group-data-[collapsible=icon]:block hidden" }}
                >
                  <a>
                    {item.icon}
                    <span>{item.title}</span>
                  </a>
                </SidebarMenuButton>
              </Link>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarContent>
      <SidebarSeparator />
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <Link href={settingsNavItem.href} legacyBehavior passHref>
              <SidebarMenuButton 
                asChild 
                isActive={isActive(settingsNavItem.href)}
                tooltip={{ children: settingsNavItem.title, className: "group-data-[collapsible=icon]:block hidden" }}
              >
                <a>
                  {settingsNavItem.icon}
                  <span>{settingsNavItem.title}</span>
                </a>
              </SidebarMenuButton>
            </Link>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}
