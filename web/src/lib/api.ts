import { API_BASE_URL } from '@/lib/config';
import type {
  ApiResponse,
  Project, ProjectCreateInput, ProjectUpdateInput,
  TestSuite, TestSuiteCreateInput, TestSuiteUpdateInput,
  TestCase, TestCaseCreateInput, TestCaseUpdateInput, TestCaseStatusUpdateInput,
  ApiHealth,
  TestCaseExecutionResponse,
  SuiteExecutionResponse,
  TestExecutionHistoryData,
  GenerateGherkinInput, GenerateGherkinOutput,
  GenerateCodeInput, GenerateCodeOutput,
  EnhanceUserStoryInput, EnhanceUserStoryOutput,
  ExecuteSmokeTestInput, ExecuteSmokeTestOutput,
  GenerateManualTestCasesInput, GenerateManualTestCasesOutput,
  SummarizeTestResultsInput, SummarizeTestResultsOutput
} from '@/lib/types';


async function fetchApi<T>(url: string, options?: RequestInit): Promise<T> {
  const response = await fetch(`${API_BASE_URL}${url}`, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...(options?.headers),
    },
  });

  if (!response.ok) {
    let errorData;
    let errorMessage = `HTTP error! status: ${response.status} for URL: ${url}`;
    try {
      const contentType = response.headers.get("content-type");
      if (contentType && contentType.indexOf("application/json") !== -1) {
        errorData = await response.json();
        errorMessage = errorData?.error || errorData?.details || errorMessage;
      } else {
        const errorText = await response.text();
        // If HTML error page, include a snippet.
        if (errorText.toLowerCase().includes("<!doctype html")) {
            errorMessage = `${errorMessage}. Server returned an HTML error page. Snippet: ${errorText.substring(0, 200)}...`;
        } else {
            errorMessage = `${errorMessage}. Response: ${errorText.substring(0, 500)}${errorText.length > 500 ? '...' : ''}`;
        }
      }
    } catch (e) {
      // If parsing as JSON or text fails, stick with the basic error from statusText or status code.
      errorMessage = `${response.statusText || `HTTP error! status: ${response.status} for URL: ${url}`}. Could not parse error response.`;
    }
    throw new Error(errorMessage);
  }
  // Handle cases where response is OK but content might be empty for 204 No Content
  if (response.status === 204) {
    return {} as T; // Or handle as appropriate for your application
  }
  return response.json();
}

// Project Endpoints
export const getProjects = (): Promise<ApiResponse<Project>> => fetchApi<ApiResponse<Project>>('/projects/');
export const createProject = (data: ProjectCreateInput): Promise<Project> => fetchApi<Project>('/projects/', { method: 'POST', body: JSON.stringify(data) });
export const getProjectById = (projectId: string): Promise<Project> => fetchApi<Project>(`/projects/${projectId}`);
export const updateProject = (projectId: string, data: ProjectUpdateInput): Promise<Project> => fetchApi<Project>(`/projects/${projectId}`, { method: 'PUT', body: JSON.stringify(data) });
export const deleteProject = (projectId: string): Promise<void> => fetchApi<void>(`/projects/${projectId}`, { method: 'DELETE' });

// Test Suite Endpoints
export const getSuitesByProjectId = (projectId: string): Promise<ApiResponse<TestSuite>> => fetchApi<ApiResponse<TestSuite>>(`/projects/${projectId}/suites`);
export const createSuite = (projectId: string, data: TestSuiteCreateInput): Promise<TestSuite> => fetchApi<TestSuite>(`/projects/${projectId}/suites/`, { method: 'POST', body: JSON.stringify(data) });
export const getSuiteById = (projectId: string, suiteId: string): Promise<TestSuite> => fetchApi<TestSuite>(`/projects/${projectId}/suites/${suiteId}`);
export const updateSuite = (projectId: string, suiteId: string, data: TestSuiteUpdateInput): Promise<TestSuite> => fetchApi<TestSuite>(`/projects/${projectId}/suites/${suiteId}`, { method: 'PUT', body: JSON.stringify(data) });
export const deleteSuite = (projectId: string, suiteId: string): Promise<void> => fetchApi<void>(`/projects/${projectId}/suites/${suiteId}`, { method: 'DELETE' });
export const executeSuite = (projectId: string, suiteId: string): Promise<SuiteExecutionResponse> => fetchApi<SuiteExecutionResponse>(`/projects/${projectId}/suites/${suiteId}/execute`, { method: 'POST' });

// Test Case Endpoints
export const getTestCasesBySuiteId = (projectId: string, suiteId: string): Promise<ApiResponse<TestCase>> => fetchApi<ApiResponse<TestCase>>(`/projects/${projectId}/suites/${suiteId}/tests`);
export const createTestCase = (projectId: string, suiteId: string, data: TestCaseCreateInput): Promise<TestCase> => fetchApi<TestCase>(`/projects/${projectId}/suites/${suiteId}/tests/`, { method: 'POST', body: JSON.stringify(data) });
export const getTestCaseById = (projectId: string, suiteId: string, testId: string): Promise<TestCase> => fetchApi<TestCase>(`/projects/${projectId}/suites/${suiteId}/tests/${testId}`);
export const updateTestCase = (projectId: string, suiteId: string, testId: string, data: TestCaseUpdateInput): Promise<TestCase> => fetchApi<TestCase>(`/projects/${projectId}/suites/${suiteId}/tests/${testId}`, { method: 'PUT', body: JSON.stringify(data) });
export const updateTestCaseStatus = (projectId: string, suiteId: string, testId: string, data: TestCaseStatusUpdateInput): Promise<TestCase> => fetchApi<TestCase>(`/projects/${projectId}/suites/${suiteId}/tests/${testId}/status`, { method: 'PATCH', body: JSON.stringify(data) });
export const deleteTestCase = (projectId: string, suiteId: string, testId: string): Promise<void> => fetchApi<void>(`/projects/${projectId}/suites/${suiteId}/tests/${testId}`, { method: 'DELETE' });
export const executeTestCase = (projectId: string, suiteId: string, testId: string): Promise<TestCaseExecutionResponse> => fetchApi<TestCaseExecutionResponse>(`/projects/${projectId}/suites/${suiteId}/tests/${testId}/execute`, { method: 'POST' });

// API Health
export const getApiHealth = (): Promise<ApiHealth> => fetchApi<ApiHealth>('/health');

export const getTestExecutionHistoryDetails = async (historyPath: string): Promise<TestExecutionHistoryData> => {
  const response = await fetch(historyPath.startsWith('http') ? historyPath : `${API_BASE_URL}/../${historyPath}`);
  if (!response.ok) {
    throw new Error(`Failed to fetch history data from ${historyPath}`);
  }
  return response.json();
};

// Test History API
export interface TestHistoryItem {
  id: string;
  name: string;
  type: 'Smoke Test' | 'Full Test';
  date: string;
  url: string;
  result: 'Exitoso' | 'Error' | 'Completado';
  screenshot_count: number;
  history_path: string;
  directory: string;
}

export interface TestHistoryResponse {
  tests: TestHistoryItem[];
  count: number;
}

export interface TestHistoryDetails {
  test_id: string;
  history: any;
  screenshots: string[];
  directory: string;
}

export const getTestHistory = (): Promise<TestHistoryResponse> =>
  fetchApi<TestHistoryResponse>('/tests/history');

export const getTestHistoryDetails = (testId: string): Promise<TestHistoryDetails> =>
  fetchApi<TestHistoryDetails>(`/tests/history/${testId}`);

export const getTestScreenshot = (testId: string, filename: string): string =>
  `${API_BASE_URL}/tests/screenshots/${testId}/${filename}`;


// AI Tool Endpoints

// Uses the API endpoint from OpenAPI spec
export async function callEnhanceUserStory(input: EnhanceUserStoryInput): Promise<EnhanceUserStoryOutput> {
  // Converting our input to match the API schema
  const apiInput = {
    user_story: input.userStory,
    language: input.language
  };

  try {
    // Using the documented endpoint
    const result = await fetchApi<any>('/stories/enhance', {
      method: 'POST',
      body: JSON.stringify(apiInput),
    });
    return {
      enhancedUserStory: result.enhanced_story || result.enhancedUserStory || '',
    };
  } catch (error) {
    throw new Error(`Failed to enhance user story: ${(error as Error).message}`);
  }
}

// Uses the API endpoint from OpenAPI spec
export async function callGenerateManualTestCases(input: GenerateManualTestCasesInput): Promise<GenerateManualTestCasesOutput> {
  // Converting our input to match the API schema
  const apiInput = {
    enhanced_story: input.userStory,
    language: input.language
  };

  try {
    // Using the documented endpoint
    const result = await fetchApi<any>('/stories/generate-manual-tests', {
      method: 'POST',
      body: JSON.stringify(apiInput),
    });    // Convert API response to our expected output format
    let manualTestCases: (string | any)[] = [];
    
    // Handle different response formats from the API
    if (result && result.manual_tests) {
      if (Array.isArray(result.manual_tests)) {
        // Check if it's an array of objects (new format) or strings (old format)
        if (result.manual_tests.length > 0 && typeof result.manual_tests[0] === 'object') {
          // New format: array of test case objects - keep as objects
          manualTestCases = result.manual_tests;
        } else {
          // Old format: direct array of strings
          manualTestCases = result.manual_tests;
        }
      } else if (typeof result.manual_tests === 'string') {
        // String response - might be JSON or plain text
        try {
          // Try parsing as JSON
          const parsed = JSON.parse(result.manual_tests);
          if (Array.isArray(parsed)) {
            if (parsed.length > 0 && typeof parsed[0] === 'object') {
              // Array of objects - keep as objects
              manualTestCases = parsed;
            } else {
              // Array of strings
              manualTestCases = parsed;
            }
          } else {
            // Create a single item array if it's an object
            manualTestCases = [parsed];
          }
        } catch (e) {
          // If parsing fails, split lines
          manualTestCases = result.manual_tests.split('\n').filter((line: string) => line.trim().length > 0);
        }
      }
    } else if (result && Array.isArray(result.manualTestCases)) {
      // Alternative field name
      manualTestCases = result.manualTestCases;
    } else if (result && typeof result === 'object') {
      // Try to use the result directly
      manualTestCases = [result];
    }

    return { manualTestCases };
  } catch (error) {
    console.error("Manual test generation error:", error);
    throw new Error(`Failed to generate manual test cases: ${(error as Error).message}`);
  }
}

// Uses the API endpoint from OpenAPI spec
export async function callGenerateGherkin(input: GenerateGherkinInput): Promise<GenerateGherkinOutput> {
  // For generating Gherkin from manual test cases
  if (input.instructions) {
    try {
      // Using the documented endpoint for generating Gherkin from manual tests
      const apiInput = {
        manual_tests: Array.isArray(input.instructions)
          ? input.instructions.join('\n')
          : input.instructions,
        language: input.language
      };

      const result = await fetchApi<any>('/stories/generate-gherkin', {
        method: 'POST',
        body: JSON.stringify(apiInput),
      });

      // Convert API response to our expected output format
      return {
        gherkin: result.gherkin || result.gherkin_scenario || '',
      };
    } catch (error) {
      throw new Error(`Failed to generate Gherkin: ${(error as Error).message}`);
    }
  }
  // For generating Gherkin directly from instructions and URL
  else {
    try {
      // Using the documented endpoint for generating Gherkin directly
      const apiInput = {
        instructions: input.userStory || '',
        url: input.url || '',
        user_story: input.userStory || '',
        language: input.language
      };

      const result = await fetchApi<any>('/generate/gherkin', {
        method: 'POST',
        body: JSON.stringify(apiInput),
      });

      // Convert API response to our expected output format
      return {
        gherkin: result.gherkin || result.gherkin_scenario || '',
      };
    } catch (error) {
      throw new Error(`Failed to generate Gherkin: ${(error as Error).message}`);
    }
  }
}


// Uses the API endpoint from OpenAPI spec for code generation
export async function callGenerateCode(input: GenerateCodeInput): Promise<GenerateCodeOutput> {
  try {
    const apiInput = {
      framework: input.framework,
      gherkin_scenario: input.gherkin_scenario,
      test_history: input.test_history || {}
    };

    const result = await fetchApi<any>('/generate/code', {
      method: 'POST',
      body: JSON.stringify(apiInput),
    });

    return {
      code: result.code || '',
    };
  } catch (error) {
    let errorMessage = `Failed to generate code: ${(error as Error).message}`;
    throw new Error(errorMessage);
  }
}

// Uses the API endpoint from OpenAPI spec for smoke test execution
export async function callExecuteSmokeTest(input: ExecuteSmokeTestInput): Promise<ExecuteSmokeTestOutput> {
  // Converting our input to match the API schema for smoke tests
  const apiInput = {
    instructions: input.instructions,
    url: input.baseUrl,
    user_story: input.userStory
  };

  try {
    // Using the documented smoke test endpoint
    const result = await fetchApi<any>('/tests/smoke', {
      method: 'POST',
      body: JSON.stringify(apiInput),
    });

    // Convert API response to our expected output format based on TestExecutionHistoryData schema
    const executionData: ExecuteSmokeTestOutput = {
      actions: result.actions || [],
      results: result.results || [],
      elements: result.elements || [],
      urls: result.urls || [],
      errors: result.errors || [],
      screenshots: result.screenshots || [],
      metadata: {
        start_time: result.start_time || new Date().toISOString(),
        end_time: result.end_time || new Date().toISOString(),
        total_steps: result.total_steps || 0,
        success: result.success !== undefined ? result.success : true
      },
      generatedGherkin: result.generated_gherkin || result.generatedGherkin || '',
      userStory: input.userStory
    };

    return executionData;
  } catch (error) {
    throw new Error(`Failed to execute smoke test: ${(error as Error).message}`);
  }
}

// Uses the API endpoint from OpenAPI spec for full test execution
export async function callExecuteFullTest(input: { gherkin: string, url?: string }): Promise<ExecuteSmokeTestOutput> {
  // Converting our input to match the API schema for full tests
  const apiInput = {
    gherkin_scenario: input.gherkin,
    url: input.url
  };

  try {
    // Using the documented full test endpoint
    const result = await fetchApi<any>('/tests/full', {
      method: 'POST',
      body: JSON.stringify(apiInput),
    });

    // Convert API response to our expected output format based on TestExecutionHistoryData schema
    const executionData: ExecuteSmokeTestOutput = {
      actions: result.actions || [],
      results: result.results || [],
      elements: result.elements || [],
      urls: result.urls || [],
      errors: result.errors || [],
      screenshots: result.screenshots || [],
      metadata: {
        start_time: result.start_time || new Date().toISOString(),
        end_time: result.end_time || new Date().toISOString(),
        total_steps: result.total_steps || 0,
        success: result.success !== undefined ? result.success : true
      },
      generatedGherkin: input.gherkin // Use the input Gherkin since this was a full test
    };

    return executionData;
  } catch (error) {
    throw new Error(`Failed to execute full test: ${(error as Error).message}`);
  }
}

// Function to save test history to a project
export async function saveTestHistory(input: {
  projectId: string,
  suiteId: string,
  name: string,
  description: string,
  gherkin: string,
  testHistory: any
}): Promise<any> {
  // Convert our input to match the API schema
  const apiInput = {
    project_id: input.projectId,
    suite_id: input.suiteId,
    name: input.name,
    description: input.description,
    gherkin: input.gherkin,
    test_history: input.testHistory
  };

  try {
    // Using the documented endpoint for saving test history
    return await fetchApi<any>('/projects/save-history', {
      method: 'POST',
      body: JSON.stringify(apiInput),
    });
  } catch (error) {
    throw new Error(`Failed to save test history: ${(error as Error).message}`);
  }
}

// Uses the API endpoint for summarizing test results
export async function callSummarizeTestResults(input: SummarizeTestResultsInput): Promise<SummarizeTestResultsOutput> {
  // Converting our input to match the API schema
  const apiInput = {
    test_results: input.testResults
  };

  try {
    // Using the documented endpoint for summarizing test results
    const result = await fetchApi<any>('/tests/summarize', {
      method: 'POST',
      body: JSON.stringify(apiInput),
    });

    return {
      summary: result.summary || '',
    };
  } catch (error) {
    throw new Error(`Failed to summarize test results: ${(error as Error).message}`);
  }
}

// Prompt Management API Functions
export async function fetchPrompts(): Promise<any> {
  try {
    return await fetchApi<any>('/prompts/');
  } catch (error) {
    throw new Error(`Failed to fetch prompts: ${(error as Error).message}`);
  }
}

export async function fetchPromptDetail(category: string, promptId: string): Promise<any> {
  try {
    return await fetchApi<any>(`/prompts/${category}/${promptId}`);
  } catch (error) {
    throw new Error(`Failed to fetch prompt details: ${(error as Error).message}`);
  }
}

export async function updatePrompt(
  category: string, 
  promptId: string, 
  data: {
    content: Record<string, string>;
    metadata?: Record<string, any>;
    commit_message?: string;
  }
): Promise<any> {
  try {
    return await fetchApi<any>(`/prompts/${category}/${promptId}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  } catch (error) {
    throw new Error(`Failed to update prompt: ${(error as Error).message}`);
  }
}

export async function createPrompt(data: {
  category: string;
  prompt_id: string;
  name: string;
  description: string;
  languages?: string[];
  content: Record<string, string>;
  metadata?: Record<string, any>;
}): Promise<any> {
  try {
    return await fetchApi<any>('/prompts/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  } catch (error) {
    throw new Error(`Failed to create prompt: ${(error as Error).message}`);
  }
}

export async function deletePrompt(category: string, promptId: string): Promise<any> {
  try {
    return await fetchApi<any>(`/prompts/${category}/${promptId}`, {
      method: 'DELETE',
    });
  } catch (error) {
    throw new Error(`Failed to delete prompt: ${(error as Error).message}`);
  }
}

export async function validatePrompt(category: string, promptId: string): Promise<any> {
  try {
    return await fetchApi<any>(`/prompts/${category}/${promptId}/validate`, {
      method: 'POST',
    });
  } catch (error) {
    throw new Error(`Failed to validate prompt: ${(error as Error).message}`);
  }
}

export async function validateAllPrompts(): Promise<any> {
  try {
    return await fetchApi<any>('/prompts/validate-all', {
      method: 'POST',
    });
  } catch (error) {
    throw new Error(`Failed to validate all prompts: ${(error as Error).message}`);
  }
}
